import React, { useState, useEffect, Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { ThemeProvider } from './context/ThemeContext';
import Navbar from './components/layout/Navbar';
import Footer from './components/layout/Footer';
import Hero from './components/sections/Hero';
import About from './components/sections/About';
import Team from './components/sections/Team';
import Services from './components/sections/Services';
import Contact from './components/sections/Contact';
import WaterWave from './components/ui/WaterWave';
import Loading from './components/ui/Loading';
import ScrollToTop from './components/ui/ScrollToTop';
import WhatsAppButton from './components/ui/WhatsAppButton';
import ErrorBoundary from './components/ui/ErrorBoundary';
import PageTransition from './components/ui/PageTransition';
import SuspenseLoader from './components/ui/SuspenseLoader';
import PerformanceMonitor from './components/ui/PerformanceMonitor';

// Lazy load heavy components for better performance
const Gallery = lazy(() => import('./components/sections/Gallery'));
const Projects = lazy(() => import('./components/sections/Projects'));
const QuoteRequest = lazy(() => import('./pages/QuoteRequest/QuoteRequest'));
// Calculator temporarily disabled for next production release
// const Calculator = lazy(() => import('./pages/Calculator/Calculator'));

// Component to handle route animations
const AnimatedRoutes: React.FC = () => {
  const location = useLocation();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <Routes location={location} key={location.pathname}>
        <Route
          path="/"
          element={
            <PageTransition>
              <Hero />
              <About />
              <Services />
              <Team />
              <Contact />
            </PageTransition>
          }
        />
        <Route
          path="/projects"
          element={
            <PageTransition>
              <Suspense fallback={<SuspenseLoader />}>
                <Projects />
              </Suspense>
            </PageTransition>
          }
        />
        <Route
          path="/gallery"
          element={
            <PageTransition>
              <Suspense fallback={<SuspenseLoader />}>
                <Gallery />
              </Suspense>
            </PageTransition>
          }
        />
        <Route
          path="/get-a-quote"
          element={
            <PageTransition>
              <Suspense fallback={<SuspenseLoader />}>
                <QuoteRequest />
              </Suspense>
            </PageTransition>
          }
        />
        {/* Calculator route temporarily disabled for next production release */}
        {/* <Route
          path="/calculator"
          element={
            <PageTransition>
              <Suspense fallback={<SuspenseLoader />}>
                <Calculator />
              </Suspense>
            </PageTransition>
          }
        /> */}
      </Routes>
    </AnimatePresence>
  );
};

function App() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Reduced loading time for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000); // Reduced from 2000ms to 1000ms

    return () => clearTimeout(timer);
  }, []);

  const handlePerformanceMetrics = (metrics: any) => {
    // You can send these metrics to analytics service
    console.log('App Performance:', metrics);
  };

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <Router
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <PerformanceMonitor onLoadComplete={handlePerformanceMetrics} />
          <Loading isLoading={isLoading} />
          {!isLoading && (
            <div className="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
              <Navbar />
              <main className="relative">
                <AnimatedRoutes />
              </main>
              <Footer />
              <WaterWave />
              <ScrollToTop />
              <WhatsAppButton />
            </div>
          )}
        </Router>
      </ThemeProvider>
    </ErrorBoundary>
  );
}

export default App;